/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Colors */
  --orange-400: hsl(12, 88%, 59%);
  --blue-950: hsl(228, 39%, 23%);
  --gray-950: hsl(233, 12%, 13%);
  --orange-50: hsl(13, 100%, 96%);
  --gray-50: hsl(0, 0%, 98%);
  --white: hsl(0, 0%, 100%);
  
  /* Typography */
  --font-family: 'Be Vietnam Pro', sans-serif;
  --font-size-base: 16px;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* Spacing */
  --container-max-width: 1110px;
  --container-padding: 1.5rem;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: 1.6;
  color: var(--gray-950);
  overflow-x: hidden;
}

.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 50px;
  font-family: var(--font-family);
  font-size: 0.8125rem;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 15px 15px -10px rgba(255, 125, 26, 0.25);
}

.btn--primary {
  background-color: var(--orange-400);
  color: var(--white);
}

.btn--primary:hover {
  background-color: hsl(12, 88%, 69%);
  box-shadow: 0 15px 15px -10px rgba(255, 125, 26, 0.4);
}

.btn--secondary {
  background-color: var(--white);
  color: var(--orange-400);
}

.btn--secondary:hover {
  color: hsl(12, 88%, 69%);
}

/* Header */
.header {
  position: relative;
  padding: 2rem 0;
  background-color: var(--white);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav__logo img {
  height: 1.5rem;
}

.nav__menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav__link {
  color: var(--blue-950);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  font-size: 0.8125rem;
  transition: color 0.3s ease;
}

.nav__link:hover {
  color: var(--gray-950);
}

.nav__toggle {
  display: none;
  cursor: pointer;
}

.nav__close {
  display: none;
}

/* Hero Section */
.hero {
  padding: 4rem 0;
  background-image: url('./images/bg-tablet-pattern.svg');
  background-repeat: no-repeat;
  background-position: top -6rem right -6rem;
  background-size: 50rem;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero__title {
  font-size: 3.5rem;
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  color: var(--blue-950);
  margin-bottom: 2rem;
}

.hero__description {
  color: var(--gray-950);
  margin-bottom: 2rem;
  max-width: 350px;
}

.hero__image img {
  width: 100%;
  height: auto;
}

/* Features Section */
.features {
  padding: 6rem 0;
}

.features__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.features__title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--blue-950);
  margin-bottom: 2rem;
  max-width: 350px;
}

.features__description {
  color: var(--gray-950);
  max-width: 350px;
}

.features__list {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.feature {
  position: relative;
}

.feature__header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.feature__number {
  background-color: var(--orange-400);
  color: var(--white);
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-weight: var(--font-weight-bold);
  font-size: 0.875rem;
}

.feature__title {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  color: var(--blue-950);
}

.feature__description {
  color: var(--gray-950);
  margin-left: 5.5rem;
}

/* Testimonials Section */
.testimonials {
  padding: 6rem 0;
  text-align: center;
}

.testimonials__title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--blue-950);
  margin-bottom: 4rem;
}

.testimonials__slider {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  padding: 0 1rem;
}

.testimonial {
  flex: 0 0 350px;
  background-color: var(--gray-50);
  padding: 4rem 2rem 2rem;
  border-radius: 0.5rem;
  position: relative;
  scroll-snap-align: center;
}

.testimonial__avatar {
  width: 4.5rem;
  height: 4.5rem;
  position: absolute;
  top: -2.25rem;
  left: 50%;
  transform: translateX(-50%);
}

.testimonial__name {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  color: var(--blue-950);
  margin-bottom: 1rem;
}

.testimonial__text {
  color: var(--gray-950);
  font-size: 0.875rem;
  line-height: 1.8;
}

.testimonials__dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 3rem;
}

.dot {
  width: 0.5rem;
  height: 0.5rem;
  border: 1px solid var(--orange-400);
  border-radius: 50%;
  background-color: transparent;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dot.active,
.dot:hover {
  background-color: var(--orange-400);
}

/* CTA Section */
.cta {
  background-color: var(--orange-400);
  background-image: url('./images/bg-simplify-section-desktop.svg');
  background-repeat: no-repeat;
  background-position: center;
  padding: 4rem 0;
}

.cta__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cta__title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--white);
  max-width: 450px;
}

/* Footer */
.footer {
  background-color: var(--gray-950);
  padding: 4rem 0 2rem;
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.footer__newsletter {
  grid-column: 1;
}

.newsletter-form {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.newsletter-form__input {
  flex: 1;
  padding: 0.875rem 1rem;
  border: none;
  border-radius: 50px;
  font-family: var(--font-family);
  font-size: 0.8125rem;
}

.newsletter-form__input:focus {
  outline: 2px solid var(--orange-400);
}

.newsletter-form__btn {
  padding: 0.875rem 1.5rem;
  font-size: 0.8125rem;
}

.newsletter-form__error {
  color: var(--orange-400);
  font-size: 0.75rem;
  margin-left: 1rem;
  font-style: italic;
}

.footer__nav {
  grid-column: 2 / 4;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.footer__links {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer__link {
  color: var(--white);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.footer__link:hover {
  color: var(--orange-400);
}

.footer__social {
  grid-column: 4;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.social-link {
  display: block;
  transition: opacity 0.3s ease;
}

.social-link:hover {
  opacity: 0.7;
}

.social-link img {
  width: 1.5rem;
  height: 1.5rem;
  filter: brightness(0) invert(1);
}

.social-link:hover img {
  filter: brightness(0) saturate(100%) invert(60%) sepia(93%) saturate(1352%) hue-rotate(346deg) brightness(100%) contrast(94%);
}

.footer__logo {
  grid-column: 4;
  justify-self: end;
  margin-top: 2rem;
}

.footer__logo img {
  height: 1.5rem;
  filter: brightness(0) invert(1);
}

.footer__copyright {
  grid-column: 1;
  color: var(--gray-50);
  font-size: 0.75rem;
  margin-top: 2rem;
  opacity: 0.7;
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  padding: 1rem;
  background-color: var(--gray-950);
  color: var(--white);
}

.attribution a {
  color: var(--orange-400);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  /* Header Mobile */
  .header {
    padding: 1.5rem 0;
  }

  .nav__menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1000;
  }

  .nav__menu.active {
    transform: translateX(0);
  }

  .nav__menu::before {
    content: '';
    position: absolute;
    top: 5rem;
    left: 1rem;
    right: 1rem;
    bottom: 10rem;
    background-color: var(--white);
    border-radius: 0.5rem;
  }

  .nav__item {
    position: relative;
    z-index: 1001;
  }

  .nav__link {
    font-size: 1rem;
    font-weight: var(--font-weight-bold);
  }

  .nav__cta {
    display: none;
  }

  .nav__toggle {
    display: block;
    z-index: 1002;
  }

  .nav__hamburger {
    display: block;
  }

  .nav__close {
    display: none;
  }

  .nav__toggle.active .nav__hamburger {
    display: none;
  }

  .nav__toggle.active .nav__close {
    display: block;
  }

  /* Hero Mobile */
  .hero {
    padding: 2rem 0;
    background-position: top -4rem right -8rem;
    background-size: 25rem;
    text-align: center;
  }

  .hero__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .hero__text {
    order: 2;
  }

  .hero__image {
    order: 1;
  }

  .hero__title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .hero__description {
    max-width: none;
    margin: 0 auto 2rem;
  }

  /* Features Mobile */
  .features {
    padding: 4rem 0;
  }

  .features__content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .features__title {
    font-size: 2rem;
    max-width: none;
    margin: 0 auto 1rem;
  }

  .features__description {
    max-width: none;
  }

  .features__list {
    text-align: left;
    gap: 2rem;
  }

  .feature__header {
    background-color: var(--orange-50);
    padding: 0.5rem 0;
    border-radius: 50px 0 0 50px;
    margin-left: -1rem;
    padding-left: 1rem;
  }

  .feature__description {
    margin-left: 0;
    margin-top: 1rem;
  }

  /* Testimonials Mobile */
  .testimonials {
    padding: 4rem 0;
  }

  .testimonials__title {
    font-size: 2rem;
    margin-bottom: 3rem;
  }

  .testimonials__slider {
    margin-bottom: 2rem;
    padding: 0;
  }

  .testimonial {
    flex: 0 0 280px;
    padding: 3rem 1.5rem 1.5rem;
  }

  .testimonials__cta {
    margin-top: 1rem;
  }

  /* CTA Mobile */
  .cta {
    background-image: url('./images/bg-simplify-section-mobile.svg');
    padding: 3rem 0;
    text-align: center;
  }

  .cta__content {
    flex-direction: column;
    gap: 2rem;
  }

  .cta__title {
    font-size: 2rem;
    max-width: none;
  }

  /* Footer Mobile */
  .footer {
    padding: 3rem 0 1rem;
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .footer__newsletter {
    order: 4;
  }

  .footer__nav {
    order: 2;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .footer__links {
    gap: 0.75rem;
  }

  .footer__social {
    order: 3;
    justify-content: center;
    gap: 2rem;
  }

  .social-link img {
    width: 2rem;
    height: 2rem;
  }

  .footer__logo {
    order: 1;
    justify-self: center;
    margin-top: 0;
  }

  .footer__logo img {
    height: 2rem;
  }

  .footer__copyright {
    order: 5;
    margin-top: 0;
    text-align: center;
  }
}
